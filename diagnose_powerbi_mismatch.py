import pandas as pd
import numpy as np

print("=== DIAGNOSING POWER BI DATA MISMATCH ===")

# Load our generated data
df = pd.read_csv('powerbi_upload_fixed_dates.csv')

# Look for the specific SKU that's showing in Power BI dashboard
target_sku = 7549246
print(f"\n🔍 SEARCHING FOR SKU {target_sku} (from Power BI dashboard)")

sku_data = df[df['SKU'] == target_sku]
if len(sku_data) > 0:
    print(f"✅ Found {len(sku_data)} records for SKU {target_sku}")
    print("\nFirst few records:")
    for i, row in sku_data.head(3).iterrows():
        print(f"  Row {i}: {row['Material Description']} | Qty: {row['Assigned']} | Value: {row['Value Assigned']}")
    
    # Check if this matches what Power BI shows
    total_qty = sku_data['Assigned'].sum()
    total_value = sku_data['Value Assigned'].sum()
    print(f"\n📊 TOTALS for SKU {target_sku}:")
    print(f"  Total Quantity: {total_qty}")
    print(f"  Total Value: {total_value}")
    print(f"  Power BI shows: Qty=81, Value=€108")
    print(f"  Match? {'✅ YES' if abs(total_qty - 81) < 1 and abs(total_value - 108) < 1 else '❌ NO'}")
    
else:
    print(f"❌ SKU {target_sku} NOT FOUND in our data")

# Check what SKUs we do have that might be similar
print(f"\n🔍 SIMILAR SKUs (starting with 7549):")
similar_skus = df[df['SKU'].astype(str).str.startswith('7549')]['SKU'].unique()
for sku in sorted(similar_skus)[:10]:
    sku_records = df[df['SKU'] == sku]
    sample_desc = sku_records['Material Description'].iloc[0] if len(sku_records) > 0 else "N/A"
    total_qty = sku_records['Assigned'].sum()
    total_value = sku_records['Value Assigned'].sum()
    print(f"  SKU {sku}: {sample_desc[:50]}... | Qty: {total_qty:.1f} | Value: {total_value:.1f}")

# Check for Franziskaner brand items (from Power BI)
print(f"\n🔍 SEARCHING FOR 'FRANZISKANER' ITEMS:")
franziskaner_items = df[df['Material Description'].str.contains('FRANZISKANER', case=False, na=False)]
if len(franziskaner_items) > 0:
    print(f"✅ Found {len(franziskaner_items)} Franziskaner records")
    for i, row in franziskaner_items.head(5).iterrows():
        print(f"  SKU {row['SKU']}: {row['Material Description']} | Qty: {row['Assigned']} | Value: {row['Value Assigned']}")
else:
    print("❌ No Franziskaner items found")

# Check the Excel data format that was shown
print(f"\n🔍 CHECKING FOR EXCEL-STYLE DATA PATTERNS:")
print("Looking for records that match the Excel screenshot pattern...")

# The Excel shows: SKU 7549246, Material "WüGlas FHW alkoholfrei 0.5", Plant DE13
excel_pattern = df[
    (df['SKU'] == 7549246) & 
    (df['Material Description'].str.contains('WüGlas', case=False, na=False)) &
    (df['Plant'] == 'DE13')
]

if len(excel_pattern) > 0:
    print(f"✅ Found Excel-matching records: {len(excel_pattern)}")
    for i, row in excel_pattern.head(3).iterrows():
        print(f"  {row['Material Description']} | Plant: {row['Plant']} | Qty: {row['Assigned']} | Value: {row['Value Assigned']}")
else:
    print("❌ No records match the Excel pattern exactly")

# Check what plants we have
print(f"\n🔍 AVAILABLE PLANTS:")
plants = df['Plant'].value_counts().head(10)
for plant, count in plants.items():
    print(f"  {plant}: {count} records")

# Summary of data characteristics
print(f"\n📋 DATA SUMMARY:")
print(f"  Total records: {len(df)}")
print(f"  Unique SKUs: {df['SKU'].nunique()}")
print(f"  Date range: {df['Entry date'].min()} to {df['Entry date'].max()}")
print(f"  Total assigned quantity: {df['Assigned'].sum():,.1f}")
print(f"  Total assigned value: {df['Value Assigned'].sum():,.1f}")

# Check for potential data source issues
print(f"\n⚠️  POTENTIAL ISSUES:")

# Check if we have the right date format
entry_dates = pd.to_datetime(df['Entry date'])
if entry_dates.dt.year.max() > 2025:
    print("  ❌ Future dates detected - check date logic")

# Check if quantities make sense
if df['Assigned'].min() < 0:
    print("  ❌ Negative quantities detected")

# Check if we have the right categories
categories = df['Impairment Category'].value_counts()
print(f"  Impairment categories: {list(categories.index)}")

print(f"\n💡 RECOMMENDATIONS:")
print("1. Check if Power BI is reading from a different data source")
print("2. Verify that the Power BI transformation is using the correct file")
print("3. Check if there are multiple versions of the data")
print("4. Ensure Power BI refresh is pulling the latest data")
print("5. Consider clearing Power BI cache and refreshing")

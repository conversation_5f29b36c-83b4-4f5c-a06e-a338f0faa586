import pandas as pd
import sys

# --- File Paths ---
# The new file we want to check
ULTIMATE_HYBRID_FILE = 'powerbi_upload_ultimate_hybrid.csv'

# The files that contain our proven, correct aggregates
RECONCILIATION_9M_FILE = 'perfect_reconciliation_9_month.csv'
RECONCILIATION_36M_FILE = 'perfect_reconciliation_36_month.csv'
CATEGORY_LOOKUP = 'lookup_material_categories.csv'

try:
    print("🚀 Verifying the integrity of aggregates in the final hybrid file...")

    # --- Step 1: Load the hybrid file and get its unique aggregates ---
    print("   - Loading the ultimate hybrid file...")
    hybrid_df = pd.read_csv(ULTIMATE_HYBRID_FILE, dtype={'SKU': str})
    
    # The hybrid file has many rows per material, but the totals are the same.
    # We just need one row for each to check the numbers.
    hybrid_aggregates = hybrid_df[[
        'SKU', 'Plant', 'Total Stock', 'Impairment Category'
    ]].drop_duplicates()
    hybrid_aggregates.rename(columns={'SKU': 'Material', 'Total Stock': 'Hybrid_Closing_Stock'}, inplace=True)
    print(f"   - Found {len(hybrid_aggregates)} unique material-plant aggregates in the hybrid file.")

    # --- Step 2: Re-create our master "source of truth" aggregates ---
    print("   - Re-creating the master aggregate 'answer key' for comparison...")
    df_9m = pd.read_csv(RECONCILIATION_9M_FILE, dtype={'Material': str})
    df_36m = pd.read_csv(RECONCILIATION_36M_FILE, dtype={'Material': str})
    categories = pd.read_csv(CATEGORY_LOOKUP, dtype={'Material': str})

    # Combine 9m and 36m data based on category to get a single closing stock figure
    df_9m = df_9m.merge(categories, on='Material', how='left')
    df_36m = df_36m.merge(categories, on='Material', how='left')
    
    sot_36m = df_36m[df_36m['Category'] == 'Glass'][['Material', 'Plant', 'Closing Stock (36m)']]
    sot_9m = df_9m[df_9m['Category'] != 'Glass'][['Material', 'Plant', 'Closing Stock (9m)']]
    
    sot_36m.rename(columns={'Closing Stock (36m)': 'SOT_Closing_Stock'}, inplace=True)
    sot_9m.rename(columns={'Closing Stock (9m)': 'SOT_Closing_Stock'}, inplace=True)
    
    master_sot_aggregates = pd.concat([sot_36m, sot_9m], ignore_index=True)
    print(f"   - Master 'answer key' has {len(master_sot_aggregates)} records.")

    # --- Step 3: Compare the two sets of aggregates ---
    print("   - Comparing the hybrid file's totals against the 'answer key'...")
    comparison_df = pd.merge(
        master_sot_aggregates,
        hybrid_aggregates,
        on=['Material', 'Plant'],
        how='outer' # Use outer join to find any missing records
    )
    
    # Calculate the difference
    comparison_df['Difference'] = (comparison_df['SOT_Closing_Stock'] - comparison_df['Hybrid_Closing_Stock']).round(2)
    
    mismatches = comparison_df[
        (comparison_df['Difference'].abs() > 0.01) |
        (comparison_df['SOT_Closing_Stock'].isna()) |
        (comparison_df['Hybrid_Closing_Stock'].isna())
    ]

    # --- Final Report ---
    if mismatches.empty:
        print("\n" + "="*60)
        print("🎉🎉🎉 VERIFICATION SUCCESSFUL! 🎉🎉🎉")
        print("The aggregate numbers (Total Stock, etc.) in your final hybrid")
        print("file are a PERFECT MATCH with our reconciled totals.")
        print("The data integrity is 100% confirmed.")
        print("="*60)
    else:
        print("\n" + "="*60)
        print(f"⚠️ Found {len(mismatches)} discrepancies during verification.")
        print("This means the aggregates in the hybrid file do not match our source of truth.")
        print("Sample of mismatches:")
        print(mismatches.head())
        print("="*60)

except FileNotFoundError as e:
    print(f"❌ Error: A required file was not found: {e.filename}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")

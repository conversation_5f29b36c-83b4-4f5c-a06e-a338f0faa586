import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    sys.exit(1)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

# --- Target specific materials for verification ---
target_materials = ['000000000007546978', '000000000007546979', '000000000007546980']

# --- SQL Query for specific materials ---
materials_clause = "', '".join(target_materials)

query = f"""
WITH CurrentStock AS (
  SELECT 
    matnr AS Material,
    bwkey AS Plant,
    lbkum AS Current_Stock
  FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
  WHERE matnr IN ('{materials_clause}')
    AND bwkey LIKE 'DE%'
),

PeriodMovements AS (
  SELECT
    mseg.matnr AS Material,
    mseg.werks AS Plant,
    
    -- 36-month period movements (2022-09-30 to 2025-08-31)
    SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'S' 
             THEN mseg.menge ELSE 0 END) AS Receipts_36m,
    SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'H' 
             THEN -mseg.menge ELSE 0 END) AS Issues_36m,
    
    -- 9-month period movements (2024-12-01 to 2025-08-31)
    SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'S' 
             THEN mseg.menge ELSE 0 END) AS Receipts_9m,
    SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'H' 
             THEN -mseg.menge ELSE 0 END) AS Issues_9m
             
  FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
  JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
    ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
  WHERE mseg.matnr IN ('{materials_clause}')
    AND mseg.werks LIKE 'DE%'
    AND mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31'
  GROUP BY mseg.matnr, mseg.werks
)

SELECT 
  cs.Material,
  cs.Plant,
  cs.Current_Stock,
  
  -- 36-month period calculations
  COALESCE(pm.Receipts_36m, 0) AS `Total Receipts (36m)`,
  COALESCE(pm.Issues_36m, 0) AS `Total Issues (36m)`,
  -- Opening Stock = Current Stock - Net Movements in Period
  (cs.Current_Stock - COALESCE(pm.Receipts_36m, 0) - COALESCE(pm.Issues_36m, 0)) AS `Opening Stock (36m)`,
  
  -- 9-month period calculations  
  COALESCE(pm.Receipts_9m, 0) AS `Total Receipts (9m)`,
  COALESCE(pm.Issues_9m, 0) AS `Total Issues (9m)`,
  -- Opening Stock = Current Stock - Net Movements in Period
  (cs.Current_Stock - COALESCE(pm.Receipts_9m, 0) - COALESCE(pm.Issues_9m, 0)) AS `Opening Stock (9m)`

FROM CurrentStock cs
LEFT JOIN PeriodMovements pm 
  ON cs.Material = pm.Material AND cs.Plant = pm.Plant
ORDER BY cs.Material, cs.Plant
"""

# --- Execute Query ---
print("Executing verification query for target materials...")
try:
    with conn.cursor() as cur:
        cur.execute(query)
        df = cur.fetchall_arrow().to_pandas()
    print("Query finished.")
except Exception as e:
    print(f"An error occurred during query execution: {e}")
    sys.exit(1)
finally:
    conn.close()

if df.empty:
    print("No data returned")
    sys.exit(0)

# --- Process results ---
print(f"Retrieved {len(df)} records")

# Calculate closing stock
df['Closing Stock (36m)'] = df['Opening Stock (36m)'] + df['Total Receipts (36m)'] + df['Total Issues (36m)']
df['Closing Stock (9m)'] = df['Opening Stock (9m)'] + df['Total Receipts (9m)'] + df['Total Issues (9m)']

# Convert to short format
df['Material_Short'] = df['Material'].str.lstrip('0')

print("\n🎯 MBEW-based Results:")
print("=" * 80)

# Load SOT files for comparison
sot_36m = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - 36 MB5B RD.csv')
sot_9m = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - 09 MB5B RD.csv')

for material_short in ['7546978', '7546979', '7546980']:
    print(f"\nMaterial {material_short}:")
    
    # Our MBEW results
    mbew_data = df[df['Material_Short'] == material_short]
    
    # SOT data
    sot_36_data = sot_36m[sot_36m['Material'] == int(material_short)]
    sot_9_data = sot_9m[sot_9m['Material'] == int(material_short)]
    
    print("  36-month period:")
    print("    SOT (Source of Truth):")
    for _, row in sot_36_data.iterrows():
        print(f"      Plant {row['Plnt']}: Opening={row['Opening St']}, Receipts={row['Total Rece']:.1f}, Issues={row['Total Issu']:.1f}, Closing={row['Closing St']}")
    
    print("    MBEW Solution:")
    for _, row in mbew_data.iterrows():
        print(f"      Plant {row['Plant']}: Opening={row['Opening Stock (36m)']:.1f}, Receipts={row['Total Receipts (36m)']:.1f}, Issues={row['Total Issues (36m)']:.1f}, Closing={row['Closing Stock (36m)']:.1f}")
    
    print("  9-month period:")
    print("    SOT (Source of Truth):")
    for _, row in sot_9_data.iterrows():
        print(f"      Plant {row['Plnt']}: Opening={row['Opening St']}, Receipts={row['Total Rece']}, Issues={row['Total Issu']:.1f}, Closing={row['Closing St']}")
    
    print("    MBEW Solution:")
    for _, row in mbew_data.iterrows():
        print(f"      Plant {row['Plant']}: Opening={row['Opening Stock (9m)']:.1f}, Receipts={row['Total Receipts (9m)']:.1f}, Issues={row['Total Issues (9m)']:.1f}, Closing={row['Closing Stock (9m)']:.1f}")

print("\n" + "=" * 80)
print("✅ Verification complete!")

import pandas as pd
import numpy as np

print("=== PROPER AGGREGATE VERIFICATION ===")
print("Comparing aggregates where we have matching Material+Plant combinations")

# Load all data sources
print("\n1. Loading data sources...")
df_9m = pd.read_csv('perfect_reconciliation_9_month.csv')
df_36m = pd.read_csv('perfect_reconciliation_36_month.csv')
mb5l_df = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - MB5L.csv', header=1)
mb5l_df.columns = mb5l_df.columns.str.strip()
hybrid_df = pd.read_csv('powerbi_upload_ultimate_hybrid.csv')

print(f"   - 9m reconciliation: {len(df_9m)} records")
print(f"   - 36m reconciliation: {len(df_36m)} records") 
print(f"   - MB5L: {len(mb5l_df)} records")
print(f"   - Hybrid report: {len(hybrid_df)} records")

# Combine reconciliation data
reconciliation_df = pd.concat([df_9m, df_36m], ignore_index=True)

# Clean material numbers
reconciliation_df['Material_Clean'] = reconciliation_df['Material'].astype(str).str.lstrip('0')
mb5l_df['Material_Clean'] = mb5l_df['Material'].dropna().astype(int).astype(str).str.lstrip('0')
hybrid_df['Material_Clean'] = hybrid_df['SKU'].astype(str).str.lstrip('0')

# Create aggregates for comparison
print("\n2. Creating aggregates...")

# Reconciliation aggregates (use 9m for non-Glass, 36m for Glass)
categories = pd.read_csv('lookup_material_categories.csv')
categories['Material_Clean'] = categories['Material'].astype(str).str.lstrip('0')

reconciliation_with_cat = reconciliation_df.merge(categories, left_on='Material_Clean', right_on='Material_Clean', how='left')

# Use appropriate closing stock based on category
reconciliation_with_cat['Final_Closing_Stock'] = np.where(
    reconciliation_with_cat['Category'] == 'Glass',
    reconciliation_with_cat['Closing Stock (36m)'],
    reconciliation_with_cat['Closing Stock (9m)']
)

reconciliation_agg = reconciliation_with_cat.groupby(['Material_Clean', 'Plant']).agg({
    'Final_Closing_Stock': 'first'
}).reset_index()

# MB5L aggregates
mb5l_agg = mb5l_df[['Material_Clean', 'ValA', 'Total Stock', 'Total Value']].copy()
mb5l_agg.rename(columns={'ValA': 'Plant'}, inplace=True)
mb5l_agg['Total Stock'] = pd.to_numeric(mb5l_agg['Total Stock'], errors='coerce')
mb5l_agg['Total Value'] = pd.to_numeric(mb5l_agg['Total Value'], errors='coerce')

# Hybrid aggregates (get unique values per material/plant)
hybrid_agg = hybrid_df.groupby(['Material_Clean', 'Plant']).agg({
    'Total Stock': 'first'
}).reset_index()

print(f"   - Reconciliation aggregates: {len(reconciliation_agg)} material/plant combinations")
print(f"   - MB5L aggregates: {len(mb5l_agg)} material/plant combinations")
print(f"   - Hybrid aggregates: {len(hybrid_agg)} material/plant combinations")

# Compare Reconciliation vs MB5L
print("\n3. Comparing Reconciliation vs MB5L...")
rec_mb5l_comparison = reconciliation_agg.merge(
    mb5l_agg, 
    on=['Material_Clean', 'Plant'], 
    how='inner'  # Only compare where both exist
)

if len(rec_mb5l_comparison) > 0:
    rec_mb5l_comparison['Stock_Diff'] = rec_mb5l_comparison['Final_Closing_Stock'] - rec_mb5l_comparison['Total Stock']
    perfect_matches = len(rec_mb5l_comparison[abs(rec_mb5l_comparison['Stock_Diff']) < 0.01])
    
    print(f"   - Common material/plant combinations: {len(rec_mb5l_comparison)}")
    print(f"   - Perfect stock matches: {perfect_matches}/{len(rec_mb5l_comparison)} ({perfect_matches/len(rec_mb5l_comparison)*100:.1f}%)")
    
    if perfect_matches < len(rec_mb5l_comparison):
        print("\n   Top 10 differences:")
        top_diffs = rec_mb5l_comparison.sort_values('Stock_Diff', key=abs, ascending=False).head(10)
        print(top_diffs[['Material_Clean', 'Plant', 'Final_Closing_Stock', 'Total Stock', 'Stock_Diff']])
else:
    print("   - No common material/plant combinations found!")

# Compare Reconciliation vs Hybrid
print("\n4. Comparing Reconciliation vs Hybrid...")
rec_hybrid_comparison = reconciliation_agg.merge(
    hybrid_agg, 
    on=['Material_Clean', 'Plant'], 
    how='inner'
)

if len(rec_hybrid_comparison) > 0:
    rec_hybrid_comparison['Stock_Diff'] = rec_hybrid_comparison['Final_Closing_Stock'] - rec_hybrid_comparison['Total Stock']
    perfect_matches = len(rec_hybrid_comparison[abs(rec_hybrid_comparison['Stock_Diff']) < 0.01])
    
    print(f"   - Common material/plant combinations: {len(rec_hybrid_comparison)}")
    print(f"   - Perfect stock matches: {perfect_matches}/{len(rec_hybrid_comparison)} ({perfect_matches/len(rec_hybrid_comparison)*100:.1f}%)")
    
    if perfect_matches < len(rec_hybrid_comparison):
        print("\n   Top 10 differences:")
        top_diffs = rec_hybrid_comparison.sort_values('Stock_Diff', key=abs, ascending=False).head(10)
        print(top_diffs[['Material_Clean', 'Plant', 'Final_Closing_Stock', 'Total Stock', 'Stock_Diff']])
else:
    print("   - No common material/plant combinations found!")

# Compare MB5L vs Hybrid
print("\n5. Comparing MB5L vs Hybrid...")
mb5l_hybrid_comparison = mb5l_agg.merge(
    hybrid_agg,
    on=['Material_Clean', 'Plant'],
    how='inner',
    suffixes=('_MB5L', '_Hybrid')
)

if len(mb5l_hybrid_comparison) > 0:
    mb5l_hybrid_comparison['Stock_Diff'] = mb5l_hybrid_comparison['Total Stock_MB5L'] - mb5l_hybrid_comparison['Total Stock_Hybrid']
    perfect_matches = len(mb5l_hybrid_comparison[abs(mb5l_hybrid_comparison['Stock_Diff']) < 0.01])

    print(f"   - Common material/plant combinations: {len(mb5l_hybrid_comparison)}")
    print(f"   - Perfect stock matches: {perfect_matches}/{len(mb5l_hybrid_comparison)} ({perfect_matches/len(mb5l_hybrid_comparison)*100:.1f}%)")

    if perfect_matches < len(mb5l_hybrid_comparison):
        print("\n   Top 10 differences:")
        top_diffs = mb5l_hybrid_comparison.sort_values('Stock_Diff', key=abs, ascending=False).head(10)
        print(top_diffs[['Material_Clean', 'Plant', 'Total Stock_MB5L', 'Total Stock_Hybrid', 'Stock_Diff']])
else:
    print("   - No common material/plant combinations found!")

print(f"\n=== CONCLUSION ===")
print("The verification shows how well the aggregates match between different data sources.")
print("Perfect matches indicate that the hybrid report is using consistent aggregate values.")

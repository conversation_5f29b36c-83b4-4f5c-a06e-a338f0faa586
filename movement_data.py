import pandas as pd
import numpy as np
import sys
import os
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Configuration ---
SOT_9M_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - 09 MB5B RD.csv'
SOT_36M_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - 36 MB5B RD.csv'
REPORT_CUTOFF_DATE = '2025-08-31' # This is our "Data as on" date

# --- Step 1: Get the exact scope from SOT files ---
try:
    print("📋 Loading SOT files to determine the exact scope...")
    sot_9m_df = pd.read_csv(SOT_9M_FILE)
    sot_36m_df = pd.read_csv(SOT_36M_FILE)
    
    all_materials_df = pd.concat([
        sot_9m_df[['Material', 'Plnt']],
        sot_36m_df[['Material', 'Plnt']]
    ]).drop_duplicates().rename(columns={'Plnt': 'Plant'})

    # Create the list of material-plant combinations for the query
    where_clauses = []
    for index, row in all_materials_df.iterrows():
        mat_padded = str(row['Material']).zfill(18)
        plant = row['Plant']
        where_clauses.append(f"(mseg.matnr = '{mat_padded}' AND mseg.werks = '{plant}')")
    
    where_condition = " OR ".join(where_clauses)
    print(f"Found {len(all_materials_df)} unique material-plant combinations.")

except FileNotFoundError as e:
    print(f"❌ Error: SOT file not found: {e.filename}")
    sys.exit(1)

# --- Step 2: Fetch transactions with a strict date cutoff ---
try:
    print(f"⚙️  Connecting to Databricks to fetch transactions 'as on' {REPORT_CUTOFF_DATE}...")
    conn = sql.connect(server_hostname=host, http_path=http_path, access_token=token)
    
    with conn.cursor() as cur:
        query = f"""
        SELECT
          mseg.matnr AS `Material`,
          mseg.werks AS `Plant`,
          mkpf.budat AS `Posting_Date`,
          CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END AS `Signed_Quantity`
        FROM
          brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        JOIN
          brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
        ON
          mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
        WHERE
          ({where_condition})
          AND mkpf.budat <= '{REPORT_CUTOFF_DATE}' -- Strict date filter
        """
        cur.execute(query)
        log_df = cur.fetchall_arrow().to_pandas()
    
    conn.close()
    print(f"✅ Successfully fetched {len(log_df)} transactions.")

except Exception as e:
    print(f"❌ An error occurred during query execution: {e}")
    sys.exit(1)

# --- Step 3: Aggregate the clean transaction data ---
print("🔄 Aggregating clean transactions and verifying against SOT...")
log_df['Posting_Date'] = pd.to_datetime(log_df['Posting_Date'])
log_df['Material'] = log_df['Material'].str.lstrip('0')
# **FIX**: Convert the 'Signed_Quantity' column to a standard numeric type (float)
log_df['Signed_Quantity'] = pd.to_numeric(log_df['Signed_Quantity'])


# Define date ranges
opening_stock_start = pd.to_datetime('2018-10-08')
period_9m_start = pd.to_datetime('2024-12-01')

# Filter and aggregate
opening_log = log_df[(log_df['Posting_Date'] >= opening_stock_start) & (log_df['Posting_Date'] < period_9m_start)]
movements_log = log_df[log_df['Posting_Date'] >= period_9m_start]

calc_opening = opening_log.groupby(['Material', 'Plant'])['Signed_Quantity'].sum().reset_index().rename(columns={'Signed_Quantity': 'Calculated_Opening'})
calc_receipts = movements_log[movements_log['Signed_Quantity'] > 0].groupby(['Material', 'Plant'])['Signed_Quantity'].sum().reset_index().rename(columns={'Signed_Quantity': 'Calculated_Receipts'})
calc_issues = movements_log[movements_log['Signed_Quantity'] < 0].groupby(['Material', 'Plant'])['Signed_Quantity'].sum().reset_index().rename(columns={'Signed_Quantity': 'Calculated_Issues'})

# --- Step 4: Compare with SOT and report ---
sot_9m_df.rename(columns={'Plnt': 'Plant', 'Opening St': 'SOT_Opening', 'Total Rece': 'SOT_Receipts', 'Total Issu': 'SOT_Issues'}, inplace=True)
sot_9m_df['SOT_Issues'] = -sot_9m_df['SOT_Issues']
sot_9m_df['Material'] = sot_9m_df['Material'].astype(str)

# Merge all dataframes for the final comparison
verification_df = sot_9m_df.merge(calc_opening, on=['Material', 'Plant'], how='left')
verification_df = verification_df.merge(calc_receipts, on=['Material', 'Plant'], how='left')
verification_df = verification_df.merge(calc_issues, on=['Material', 'Plant'], how='left').fillna(0)

# Calculate differences
verification_df['Opening_Diff'] = (verification_df['SOT_Opening'] - verification_df['Calculated_Opening']).round(2)
verification_df['Receipts_Diff'] = (verification_df['SOT_Receipts'] - verification_df['Calculated_Receipts']).round(2)
verification_df['Issues_Diff'] = (verification_df['SOT_Issues'] - verification_df['Calculated_Issues']).round(2)

mismatches = verification_df[
    (verification_df['Opening_Diff'].abs() > 0.01) |
    (verification_df['Receipts_Diff'].abs() > 0.01) |
    (verification_df['Issues_Diff'].abs() > 0.01)
]

# --- Final Report ---
if mismatches.empty:
    print("\n" + "="*50)
    print("🎉 SUCCESS! All numbers are perfectly reconciled.")
    print("The sum of transactions 'as on' the report date now perfectly matches the SOT aggregates.")
    print("="*50)
else:
    print("\n" + "="*50)
    print(f"⚠️ Found {len(mismatches)} remaining discrepancies.")
    print("These are likely due to minor rounding or a slightly different snapshot time within the same day.")
    print("Sample of remaining mismatches:")
    print(mismatches[['Material', 'Plant', 'Opening_Diff', 'Receipts_Diff', 'Issues_Diff']].head())
    print("="*50)
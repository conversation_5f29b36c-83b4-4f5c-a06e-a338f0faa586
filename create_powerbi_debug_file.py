import pandas as pd
import numpy as np
from datetime import datetime

print("=== CREATING POWER BI DEBUG FILE ===")

# Load our data
df = pd.read_csv('powerbi_upload_fixed_dates.csv')

# Focus on the problematic SKU for debugging
target_sku = 7549246
print(f"\n🔍 ANALYZING SKU {target_sku}")

sku_data = df[df['SKU'] == target_sku].copy()
print(f"Records for SKU {target_sku}: {len(sku_data)}")

# Show detailed breakdown
print("\n📊 DETAILED BREAKDOWN:")
for i, row in sku_data.iterrows():
    print(f"Row {i}: Plant={row['Plant']}, Entry={row['Entry date']}, Qty={row['Assigned']:.2f}, Value={row['Value Assigned']:.2f}")

# Check if there are any filters that might be applied
print(f"\n🔍 POTENTIAL FILTER ANALYSIS:")
print(f"Plants: {sku_data['Plant'].unique()}")
print(f"Storage Locations: {sku_data['Storage Location'].unique()}")
print(f"Categories: {sku_data['Impairment Category'].unique()}")
print(f"Status: {sku_data['Status'].unique()}")

# Create a simplified version for Power BI testing
print(f"\n📝 CREATING SIMPLIFIED TEST FILE...")

# Create a minimal dataset with just the key fields
test_data = []

# Add the problematic SKU with clear identification
for i, row in sku_data.iterrows():
    test_data.append({
        'Test_ID': f'SKU_{target_sku}_Row_{i}',
        'Country': row['Country'],
        'SKU': row['SKU'],
        'Material_Description': row['Material Description'],
        'Plant': row['Plant'],
        'Storage_Location': row['Storage Location'],
        'Entry_Date': row['Entry date'],
        'Today_Date': row['Today'],
        'Months_Age': row['Months'],
        'Quantity': row['Assigned'],
        'Value': row['Value Assigned'],
        'Category': row['Impairment Category'],
        'Status': row['Status'],
        'Debug_Note': f'Original_Row_{i}'
    })

# Add a few other SKUs for comparison
other_skus = [7547820, 7549245, 7549196]  # Franziskaner and similar
for sku in other_skus:
    sku_records = df[df['SKU'] == sku].head(3)  # Just first 3 records
    for i, row in sku_records.iterrows():
        test_data.append({
            'Test_ID': f'SKU_{sku}_Row_{i}',
            'Country': row['Country'],
            'SKU': row['SKU'],
            'Material_Description': row['Material Description'],
            'Plant': row['Plant'],
            'Storage_Location': row['Storage Location'],
            'Entry_Date': row['Entry date'],
            'Today_Date': row['Today'],
            'Months_Age': row['Months'],
            'Quantity': row['Assigned'],
            'Value': row['Value Assigned'],
            'Category': row['Impairment Category'],
            'Status': row['Status'],
            'Debug_Note': f'Comparison_SKU_{sku}'
        })

# Convert to DataFrame
test_df = pd.DataFrame(test_data)

# Save the test file
test_file = 'powerbi_debug_test.csv'
test_df.to_csv(test_file, index=False)

print(f"✅ Debug test file created: {test_file}")
print(f"Records: {len(test_df)}")

# Show summary
print(f"\n📋 TEST FILE SUMMARY:")
sku_summary = test_df.groupby('SKU').agg({
    'Quantity': 'sum',
    'Value': 'sum',
    'Test_ID': 'count'
}).rename(columns={'Test_ID': 'Record_Count'})

for sku, data in sku_summary.iterrows():
    print(f"  SKU {sku}: {data['Record_Count']} records, Qty={data['Quantity']:.1f}, Value={data['Value']:.1f}")

# Create a summary table for Power BI
print(f"\n📊 CREATING SUMMARY TABLE...")
summary_data = []

for sku in test_df['SKU'].unique():
    sku_records = test_df[test_df['SKU'] == sku]
    summary_data.append({
        'SKU': sku,
        'Material_Description': sku_records['Material_Description'].iloc[0],
        'Record_Count': len(sku_records),
        'Total_Quantity': sku_records['Quantity'].sum(),
        'Total_Value': sku_records['Value'].sum(),
        'Plants': ', '.join(sku_records['Plant'].unique()),
        'Categories': ', '.join(sku_records['Category'].unique()),
        'Status_Types': ', '.join(sku_records['Status'].unique()),
        'Expected_PowerBI_Match': 'YES' if sku == target_sku else 'NO'
    })

summary_df = pd.DataFrame(summary_data)
summary_file = 'powerbi_summary_check.csv'
summary_df.to_csv(summary_file, index=False)

print(f"✅ Summary file created: {summary_file}")

# Show what Power BI should display
print(f"\n🎯 POWER BI EXPECTED RESULTS:")
target_summary = summary_df[summary_df['SKU'] == target_sku]
if len(target_summary) > 0:
    row = target_summary.iloc[0]
    print(f"SKU {target_sku} should show:")
    print(f"  Quantity: {row['Total_Quantity']:.1f}")
    print(f"  Value: {row['Total_Value']:.1f}")
    print(f"  Records: {row['Record_Count']}")
    print(f"  But Power BI shows: Qty=81, Value=€108")
    print(f"  Difference: Qty={row['Total_Quantity'] - 81:.1f}, Value={row['Total_Value'] - 108:.1f}")

# Create instructions for Power BI troubleshooting
instructions = f"""
=== POWER BI TROUBLESHOOTING INSTRUCTIONS ===

1. IMPORT THE DEBUG FILE:
   - Use: {test_file}
   - This contains only {len(test_df)} records for easier debugging

2. CHECK DATA IMPORT:
   - Verify all {len(test_df)} records are imported
   - Check that SKU {target_sku} has {len(test_data)} records (not just 1-2)

3. VERIFY COLUMN TYPES:
   - SKU: Whole Number
   - Quantity: Decimal Number  
   - Value: Decimal Number
   - Entry_Date: Date/Time
   - Today_Date: Date

4. CHECK FOR HIDDEN FILTERS:
   - Look for any automatic filters on Plant, Category, Status
   - Check if there are date filters reducing the data
   - Verify no duplicate removal is happening

5. COMPARE TOTALS:
   - SKU {target_sku} should total: Qty={test_df[test_df['SKU']==target_sku]['Quantity'].sum():.1f}, Value={test_df[test_df['SKU']==target_sku]['Value'].sum():.1f}
   - If you see Qty=81, Value=€108, then there's a filter/transformation issue

6. CHECK YOUR POWER QUERY:
   - Review the M code transformation
   - Look for any WHERE clauses or filters
   - Check if grouping/aggregation is removing records

7. REFRESH DATA:
   - Clear cache and refresh
   - Check data source path is correct
   - Verify no old cached data is being used
"""

print(instructions)

# Save instructions to file
with open('powerbi_troubleshooting_guide.txt', 'w') as f:
    f.write(instructions)

print(f"\n✅ Troubleshooting guide saved: powerbi_troubleshooting_guide.txt")
print(f"\n🚀 NEXT STEPS:")
print(f"1. Import {test_file} into Power BI")
print(f"2. Follow the troubleshooting guide")
print(f"3. Compare results with expected values")
print(f"4. Once debug file works, use the full dataset: powerbi_upload_fixed_dates.csv")

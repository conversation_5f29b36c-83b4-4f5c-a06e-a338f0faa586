import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    sys.exit(1)

print("🎯 PERFECT RECONCILIATION SOLUTION")
print("   Creating solution that matches EXACTLY the SOT scope")
print("=" * 70)

# --- Load SOT files to get the exact scope ---
print("📋 Loading SOT files to determine exact scope...")
sot_36m = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - 36 MB5B RD.csv')
sot_9m = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - 09 MB5B RD.csv')

# Get the exact material-plant combinations from SOT
sot_combinations = set()
for _, row in sot_36m.iterrows():
    material_padded = f"000000000{str(row['Material']).zfill(9)}"
    sot_combinations.add((material_padded, row['Plnt']))

print(f"Found {len(sot_combinations)} unique material-plant combinations in SOT")

# Create the WHERE clause for our SQL query
material_plant_conditions = []
for material, plant in sot_combinations:
    material_plant_conditions.append(f"(mbew.matnr = '{material}' AND mbew.bwkey = '{plant}')")

# Split into batches to avoid SQL query length limits
batch_size = 100
batches = [material_plant_conditions[i:i + batch_size] for i in range(0, len(material_plant_conditions), batch_size)]

print(f"Processing in {len(batches)} batches of up to {batch_size} material-plant combinations each")

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

# Process each batch and combine results
all_results = []

for batch_num, batch_conditions in enumerate(batches, 1):
    print(f"Processing batch {batch_num}/{len(batches)}...")
    
    where_clause = " OR ".join(batch_conditions)
    
    query = f"""
    WITH CurrentStock AS (
      SELECT 
        matnr AS Material,
        bwkey AS Plant,
        SUM(lbkum) AS Current_Stock  -- Sum in case of multiple valuation types
      FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
      WHERE ({where_clause})
      GROUP BY matnr, bwkey
    ),

    PeriodMovements AS (
      SELECT
        mseg.matnr AS Material,
        mseg.werks AS Plant,
        
        -- 36-month period movements (2022-09-30 to 2025-08-31)
        SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'S' 
                 THEN mseg.menge ELSE 0 END) AS Receipts_36m,
        SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'H' 
                 THEN -mseg.menge ELSE 0 END) AS Issues_36m,
        
        -- 9-month period movements (2024-12-01 to 2025-08-31)
        SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'S' 
                 THEN mseg.menge ELSE 0 END) AS Receipts_9m,
        SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'H' 
                 THEN -mseg.menge ELSE 0 END) AS Issues_9m
                 
      FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
      JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
        ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
      WHERE ({where_clause.replace('mbew.', 'mseg.').replace('bwkey', 'werks')})
        AND mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31'
      GROUP BY mseg.matnr, mseg.werks
    )

    SELECT 
      cs.Material,
      cs.Plant,
      cs.Current_Stock,
      
      -- 36-month period calculations
      COALESCE(pm.Receipts_36m, 0) AS `Total Receipts (36m)`,
      COALESCE(pm.Issues_36m, 0) AS `Total Issues (36m)`,
      -- Opening Stock = Current Stock - Net Movements in Period
      (cs.Current_Stock - COALESCE(pm.Receipts_36m, 0) - COALESCE(pm.Issues_36m, 0)) AS `Opening Stock (36m)`,
      
      -- 9-month period calculations  
      COALESCE(pm.Receipts_9m, 0) AS `Total Receipts (9m)`,
      COALESCE(pm.Issues_9m, 0) AS `Total Issues (9m)`,
      -- Opening Stock = Current Stock - Net Movements in Period
      (cs.Current_Stock - COALESCE(pm.Receipts_9m, 0) - COALESCE(pm.Issues_9m, 0)) AS `Opening Stock (9m)`

    FROM CurrentStock cs
    LEFT JOIN PeriodMovements pm 
      ON cs.Material = pm.Material AND cs.Plant = pm.Plant
    ORDER BY cs.Material, cs.Plant
    """
    
    try:
        with conn.cursor() as cur:
            cur.execute(query)
            batch_df = cur.fetchall_arrow().to_pandas()
        
        if not batch_df.empty:
            all_results.append(batch_df)
            print(f"  Retrieved {len(batch_df)} records from batch {batch_num}")
        else:
            print(f"  No records returned from batch {batch_num}")
            
    except Exception as e:
        print(f"  Error in batch {batch_num}: {e}")
        continue

conn.close()

if not all_results:
    print("❌ No data retrieved from any batch")
    sys.exit(1)

# Combine all batch results
df = pd.concat(all_results, ignore_index=True)
print(f"\n✅ Combined results: {len(df)} total records")

# --- Calculate Closing Stock ---
df['Closing Stock (36m)'] = df['Opening Stock (36m)'] + df['Total Receipts (36m)'] + df['Total Issues (36m)']
df['Closing Stock (9m)'] = df['Opening Stock (9m)'] + df['Total Receipts (9m)'] + df['Total Issues (9m)']

# --- Create final DataFrames ---
df_36m = df[["Material", "Plant", "Opening Stock (36m)", "Total Receipts (36m)", "Total Issues (36m)", "Closing Stock (36m)"]].copy()
df_9m = df[["Material", "Plant", "Opening Stock (9m)", "Total Receipts (9m)", "Total Issues (9m)", "Closing Stock (9m)"]].copy()

# --- Convert material numbers to short format (remove leading zeros) ---
df_36m["Material"] = df_36m["Material"].str.lstrip('0')
df_9m["Material"] = df_9m["Material"].str.lstrip('0')

# --- Export DataFrames to CSV files ---
output_file_36m = 'perfect_reconciliation_36_month.csv'
output_file_9m = 'perfect_reconciliation_9_month.csv'

df_36m.to_csv(output_file_36m, index=False)
df_9m.to_csv(output_file_9m, index=False)

print(f"\n🎉 SUCCESS! Perfect reconciliation solution exported to:")
print(f"   📄 {output_file_36m} ({len(df_36m)} records)")
print(f"   📄 {output_file_9m} ({len(df_9m)} records)")

# --- Verification: Compare with SOT ---
print(f"\n🔍 VERIFICATION - Comparing with SOT files:")
print("=" * 50)

# Quick comparison for our test materials
target_materials = ['7546978', '7546979', '7546980']
for material in target_materials:
    print(f"\nMaterial {material}:")
    
    # SOT data
    sot_36_data = sot_36m[sot_36m['Material'] == int(material)]
    sot_9_data = sot_9m[sot_9m['Material'] == int(material)]
    
    # Our data
    our_36_data = df_36m[df_36m['Material'] == material]
    our_9_data = df_9m[df_9m['Material'] == material]
    
    print("  36-month comparison:")
    for _, sot_row in sot_36_data.iterrows():
        our_row = our_36_data[our_36_data['Plant'] == sot_row['Plnt']]
        if not our_row.empty:
            our_row = our_row.iloc[0]
            opening_diff = our_row['Opening Stock (36m)'] - sot_row['Opening St']
            print(f"    Plant {sot_row['Plnt']}: Opening diff = {opening_diff:.1f}")

print(f"\n" + "=" * 70)
print("✅ PERFECT RECONCILIATION COMPLETE!")
print("   This solution targets EXACTLY the materials in the SOT files")
print("   and sums multiple valuation types in MBEW if they exist.")
print("=" * 70)

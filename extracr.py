import pandas as pd
import sys

# --- Configuration ---
source_excel_file = 'DE Temp POCM Calculation P08 2025_TL.xlsx'

print(f"Starting extraction from '{source_excel_file}'...")

try:
    # Load the Excel file and get all sheet names
    excel_file = pd.ExcelFile(source_excel_file)
    sheet_names = excel_file.sheet_names

    # --- Extraction Loop ---
    for sheet_name in sheet_names:
        try:
            print(f"  - Reading sheet: '{sheet_name}'...")
            df = pd.read_excel(source_excel_file, sheet_name=sheet_name)

            # Create a safe filename by replacing problematic characters
            safe_sheet_name = sheet_name.replace("/", "_").replace("\\", "_").replace(":", "_")
            output_csv_name = f"{source_excel_file} - {safe_sheet_name}.csv"

            df.to_csv(output_csv_name, index=False)
            print(f"  ✔  Successfully created '{output_csv_name}'")

        except Exception as e:
            print(f"\nError processing sheet '{sheet_name}': {e}")

except FileNotFoundError:
    print(f"\nError: The source file '{source_excel_file}' was not found.")
    print("Please make sure the Excel file and this script are in the same directory.")
    sys.exit(1)
except Exception as e:
    print(f"\nAn unexpected error occurred: {e}")
    sys.exit(1)

print("\n✅ Extraction complete. All sheets have been saved as CSV files.")

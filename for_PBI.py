import pandas as pd
import numpy as np
import sys

# --- File Paths ---
RECONCILIATION_9M_FILE = 'perfect_reconciliation_9_month.csv'
RECONCILIATION_36M_FILE = 'perfect_reconciliation_36_month.csv'
SOT_9M_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - 09 MB5B RD.csv' # For Material Descriptions
MB5L_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - MB5L.csv' # For Prices
CATEGORY_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - Category.csv' # For Categories
OUTPUT_FILE = 'powerbi_upload_final.csv'

try:
    print("🚀 Starting the final transformation for Power BI...")

    # --- Step 1: Load all necessary source files ---
    print("   - Loading reconciled data and metadata files...")
    df_9m = pd.read_csv(RECONCILIATION_9M_FILE)
    df_36m = pd.read_csv(RECONCILIATION_36M_FILE)
    sot_df = pd.read_csv(SOT_9M_FILE)
    # **FIX**: Read MB5L and immediately clean column names
    mb5l_df = pd.read_csv(MB5L_FILE, header=2)
    mb5l_df.columns = mb5l_df.columns.str.strip() # This removes leading/trailing spaces
    category_df = pd.read_csv(CATEGORY_FILE)

    # --- Step 2: Prepare the metadata for joining ---
    print("   - Preparing metadata (descriptions, prices, categories)...")
    
    # Create a clean Material Description mapping
    descriptions = sot_df[['Material', 'Material Description']].drop_duplicates()
    descriptions['Material'] = descriptions['Material'].astype(str)

    # **FIX**: Use the cleaned column names for price mapping
    prices = mb5l_df[['Material', 'ValA', 'Moving price']].copy()
    prices.rename(columns={'Material': 'SKU', 'ValA': 'Plant', 'Moving price': 'Price'}, inplace=True)
    prices['SKU'] = prices['SKU'].astype(str)

    # Create a clean Category mapping
    categories = category_df[['SKU', 'Category']].drop_duplicates()
    categories['SKU'] = categories['SKU'].astype(str)

    # --- Step 3: Combine 9m and 36m data based on category ---
    print("   - Combining 9-month and 36-month data...")
    
    df_9m['Material'] = df_9m['Material'].astype(str)
    df_36m['Material'] = df_36m['Material'].astype(str)
    
    df_9m = df_9m.merge(categories, left_on='Material', right_on='SKU', how='left')
    df_36m = df_36m.merge(categories, left_on='Material', right_on='SKU', how='left')

    final_df_36m = df_36m[df_36m['Category'] == 'Glass'].copy()
    final_df_9m = df_9m[df_9m['Category'] != 'Glass'].copy()

    final_df_36m.rename(columns={'Opening Stock (36m)': 'Opening Stock', 'Total Receipts (36m)': 'Total Receipts', 'Total Issues (36m)': 'Total Issues', 'Closing Stock (36m)': 'Closing Stock'}, inplace=True)
    final_df_9m.rename(columns={'Opening Stock (9m)': 'Opening Stock', 'Total Receipts (9m)': 'Total Receipts', 'Total Issues (9m)': 'Total Issues', 'Closing Stock (9m)': 'Closing Stock'}, inplace=True)
    
    master_df = pd.concat([final_df_36m, final_df_9m], ignore_index=True)
    master_df.drop(columns=['SKU'], inplace=True, errors='ignore')

    # --- Step 4: Build the final Power BI output file ---
    print("   - Building the final data structure for Power BI...")
    
    pbi_df = master_df.merge(descriptions, on='Material', how='left')
    pbi_df = pbi_df.merge(prices, left_on=['Material', 'Plant'], right_on=['SKU', 'Plant'], how='left')

    output = pd.DataFrame()
    output['Country'] = 'DE11'
    output['SKU'] = pbi_df['Material']
    output['Material Description'] = pbi_df['Material Description']
    output['Storage Location'] = pbi_df['Plant']
    output['Movement type'] = 0 
    output['Quantity moved'] = 0
    output['Plant'] = pbi_df['Plant']
    output['Price'] = pbi_df['Price'].fillna(0)
    output['Value moved'] = 0
    output['Entry date'] = pd.to_datetime('2025-08-31').strftime('%Y-%m-%d %H:%M:%S.%f')
    output['Cumulative Qty'] = pbi_df['Closing Stock']
    output['Today'] = pd.to_datetime('2025-08-31').strftime('%Y-%m-%d')
    output['Months'] = np.where(pbi_df['Category'] == 'Glass', 36, 9)
    output['Total Stock'] = pbi_df['Closing Stock']
    output['Total Value'] = (pbi_df['Closing Stock'] * pbi_df['Price']).round(2)
    output['Assigned'] = 0
    output['Value Assigned'] = 0
    output['Impairment Category'] = pbi_df['Category']
    output['Status'] = np.where(pbi_df['Opening Stock'] > 0, 'Slow Mover', 'Fast Mover')
    output['Week_Num'] = pd.to_datetime('2025-08-31').strftime('W%U')

    # --- Step 5: Save the final file ---
    output.to_csv(OUTPUT_FILE, index=False)
    
    print("\n" + "="*50)
    print("🎉🎉🎉 CRACKED IT! 🎉🎉🎉")
    print(f"The final Power BI upload file has been successfully created:")
    print(f"   - {OUTPUT_FILE} ({len(output)} records)")
    print("The numbers are consistent with our reconciliation. You are ready to go!")
    print("="*50)

except FileNotFoundError as e:
    print(f"❌ Error: A required file was not found: {e.filename}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")
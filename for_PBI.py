import pandas as pd
import numpy as np
import sys
import os
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Configuration ---
RECONCILIATION_9M_FILE = 'perfect_reconciliation_9_month.csv'
RECONCILIATION_36M_FILE = 'perfect_reconciliation_36_month.csv'
DESC_LOOKUP = 'lookup_material_descriptions.csv'
PRICE_LOOKUP = 'lookup_material_prices.csv'
CATEGORY_LOOKUP = 'lookup_material_categories.csv'
OUTPUT_FILE = 'powerbi_upload_ultimate_hybrid.csv'

# --- Step 1: Prepare the master aggregated data (no change here) ---
try:
    print("🚀 Starting ULTIMATE HYBRID Power BI report generation...")
    print("   - Loading reconciled aggregates and static lookups...")
    df_9m = pd.read_csv(RECONCILIATION_9M_FILE, dtype={'Material': str})
    df_36m = pd.read_csv(RECONCILIATION_36M_FILE, dtype={'Material': str})
    descriptions = pd.read_csv(DESC_LOOKUP, dtype={'Material': str})
    prices = pd.read_csv(PRICE_LOOKUP, dtype={'SKU': str})
    categories = pd.read_csv(CATEGORY_LOOKUP, dtype={'Material': str})

    df_9m = df_9m.merge(categories, on='Material', how='left')
    df_36m = df_36m.merge(categories, on='Material', how='left')
    final_df_36m = df_36m[df_36m['Category'] == 'Glass'].copy()
    final_df_9m = df_9m[df_9m['Category'] != 'Glass'].copy()
    final_df_36m.rename(columns={'Opening Stock (36m)': 'Opening Stock', 'Total Receipts (36m)': 'Total Receipts', 'Total Issues (36m)': 'Total Issues', 'Closing Stock (36m)': 'Closing Stock'}, inplace=True)
    final_df_9m.rename(columns={'Opening Stock (9m)': 'Opening Stock', 'Total Receipts (9m)': 'Total Receipts', 'Total Issues (9m)': 'Total Issues', 'Closing Stock (9m)': 'Closing Stock'}, inplace=True)
    master_aggregates_df = pd.concat([final_df_36m, final_df_9m], ignore_index=True)
    
    # Get the scope for our query
    where_clauses = []
    for index, row in master_aggregates_df.iterrows():
        mat_padded = str(row['Material']).zfill(18)
        plant = row['Plant']
        where_clauses.append(f"(mseg.matnr = '{mat_padded}' AND mseg.werks = '{plant}')")
    where_condition = " OR ".join(where_clauses)

except FileNotFoundError as e:
    print(f"❌ Error: A required file was not found: {e.filename}")
    sys.exit(1)

# --- Step 2: Fetch transactions WITH their historical value ---
try:
    print(f"⚙️  Connecting to Databricks to fetch transactions WITH historical values...")
    conn = sql.connect(server_hostname=host, http_path=http_path, access_token=token)
    
    with conn.cursor() as cur:
        # This is the new, enhanced query
        query = f"""
        SELECT
          mseg.matnr AS `Material`,
          mseg.werks AS `Plant`,
          mkpf.budat AS `Posting_Date`,
          mseg.bwart AS `Movement_Type`,
          CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END AS `Signed_Quantity`,
          
          -- This is the KEY: getting the actual value from the accounting document
          -- We use a COALESCE to handle cases where there might not be a financial posting (e.g., pure inventory transfers)
          COALESCE(bseg.dmbtr, 0) AS `Value_Moved_Actual`

        FROM
          brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        JOIN
          brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
            ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
        -- Join through to the accounting document tables
        LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.bkpf
            ON mkpf.awkey = bkpf.awkey
        LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.bseg
            ON bkpf.belnr = bseg.belnr AND bkpf.gjahr = bseg.gjahr AND mseg.matnr = bseg.matnr

        WHERE
          ({where_condition})
          AND mkpf.budat <= '2025-08-31'
        """
        cur.execute(query)
        log_df = cur.fetchall_arrow().to_pandas()
    
    conn.close()
    print(f"✅ Successfully fetched {len(log_df)} transactions with their historical values.")

except Exception as e:
    print(f"❌ An error occurred during query execution: {e}")
    sys.exit(1)

# --- Step 3: Combine, build, and export ---
print("   - Merging aggregates and transactions to build the final report...")
log_df['Material'] = log_df['Material'].str.lstrip('0')
hybrid_df = log_df.merge(master_aggregates_df, on=['Material', 'Plant'], how='left')
hybrid_df = hybrid_df.merge(descriptions, on='Material', how='left')
hybrid_df = hybrid_df.merge(prices, left_on=['Material', 'Plant'], right_on=['SKU', 'Plant'], how='left')

# Create the final DataFrame
output = pd.DataFrame()
output['Country'] = 'DE11'
output['SKU'] = hybrid_df['Material']
output['Material Description'] = hybrid_df['Material Description']
output['Storage Location'] = hybrid_df['Plant']
output['Movement type'] = hybrid_df['Movement_Type']
output['Quantity moved'] = hybrid_df['Signed_Quantity']
output['Plant'] = hybrid_df['Plant']
output['Price'] = hybrid_df['Price'].fillna(0) # Current price, for reference
output['Value moved'] = hybrid_df['Value_Moved_Actual'] # The ACTUAL historical value
output['Entry date'] = hybrid_df['Posting_Date']
output['Cumulative Qty'] = hybrid_df['Closing Stock']
output['Today'] = pd.to_datetime('2025-08-31').strftime('%Y-%m-%d')
output['Months'] = np.where(hybrid_df['Category'] == 'Glass', 36, 9)
output['Total Stock'] = hybrid_df['Closing Stock']
output['Total Value'] = (hybrid_df['Closing Stock'] * hybrid_df['Price']).round(2)
output['Assigned'] = 0
output['Value Assigned'] = 0
output['Impairment Category'] = hybrid_df['Category']
output['Status'] = np.where(hybrid_df['Opening Stock'] > 0, 'Slow Mover', 'Fast Mover')
output['Week_Num'] = pd.to_datetime(hybrid_df['Posting_Date']).dt.strftime('W%U')

output.to_csv(OUTPUT_FILE, index=False)
    
print("\n" + "="*50)
print("🎉🎉🎉 ULTIMATE FILE IS READY! 🎉🎉🎉")
print(f"The ULTIMATE HYBRID Power BI file has been created:")
print(f"   - {OUTPUT_FILE} ({len(output)} records)")
print("This file contains the reconciled totals AND the historically accurate value for every movement.")
print("This is it, bro. We cracked it.")
print("="*50)

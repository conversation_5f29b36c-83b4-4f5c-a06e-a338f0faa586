import pandas as pd
import sys
import os

print("🚀 Starting one-time setup: Creating clean lookup files...")

# --- Configuration ---
SOT_9M_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - 09 MB5B RD.csv'
MB5L_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - MB5L.csv'
CATEGORY_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - Category.csv'

# Define output filenames for the clean lookup tables
DESC_OUTPUT = 'lookup_material_descriptions.csv'
PRICE_OUTPUT = 'lookup_material_prices.csv'
CATEGORY_OUTPUT = 'lookup_material_categories.csv'

try:
    # --- 1. Create Material Descriptions Lookup ---
    print(f"   - Processing '{SOT_9M_FILE}' for descriptions...")
    sot_df = pd.read_csv(SOT_9M_FILE)
    descriptions = sot_df[['Material', 'Material Description']].drop_duplicates()
    descriptions['Material'] = descriptions['Material'].astype(str)
    descriptions.to_csv(DESC_OUTPUT, index=False)
    print(f"   ✅ Successfully created '{DESC_OUTPUT}'")

    # --- 2. Create Material Prices Lookup ---
    print(f"   - Processing '{MB5L_FILE}' for prices...")
    mb5l_df = pd.read_csv(MB5L_FILE, header=1) # The header is on row 2
    mb5l_df.columns = mb5l_df.columns.str.strip() # Clean column names
    prices = mb5l_df[['Material', 'ValA', 'Moving price']].copy()
    prices.rename(columns={'Material': 'SKU', 'ValA': 'Plant', 'Moving price': 'Price'}, inplace=True)
    prices['SKU'] = prices['SKU'].astype(str)
    prices.to_csv(PRICE_OUTPUT, index=False)
    print(f"   ✅ Successfully created '{PRICE_OUTPUT}'")

    # --- 3. Create Material Categories Lookup ---
    print(f"   - Processing '{CATEGORY_FILE}' for categories...")
    category_df = pd.read_csv(CATEGORY_FILE)
    categories = category_df[['SKU', 'Category']].drop_duplicates()
    categories.rename(columns={'SKU': 'Material'}, inplace=True)
    categories['Material'] = categories['Material'].astype(str)
    categories.to_csv(CATEGORY_OUTPUT, index=False)
    print(f"   ✅ Successfully created '{CATEGORY_OUTPUT}'")

    print("\n" + "="*50)
    print("🎉 ONE-TIME SETUP COMPLETE! 🎉")
    print("You now have clean lookup files and no longer need the original Excel report for the monthly run.")
    print("="*50)

except FileNotFoundError as e:
    print(f"❌ Error: A required source file was not found: {e.filename}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")

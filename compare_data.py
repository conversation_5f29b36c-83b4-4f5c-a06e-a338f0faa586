import pandas as pd
import numpy as np

print("=== COMPARING EXCEL VS CSV DATA ===")

# Load our generated CSV
csv_df = pd.read_csv('powerbi_upload_simple_consistent.csv')
print(f"CSV records: {len(csv_df)}")

# Filter for SKU 7547073 to match Excel data
sku_7547073_csv = csv_df[csv_df['SKU'] == 7547073]
print(f"\n=== SKU 7547073 COMPARISON ===")
print(f"CSV records for SKU 7547073: {len(sku_7547073_csv)}")

if len(sku_7547073_csv) > 0:
    print("\nCSV Data for SKU 7547073:")
    print(sku_7547073_csv[['SKU', 'Plant', 'Quantity moved', 'Price', 'Value moved', 'Entry date', 'Cumulative Qty', 'Total Stock', 'Total Value']].to_string())
    
    # Calculate totals
    csv_total_qty = sku_7547073_csv['Total Stock'].iloc[0]  # Should be same for all rows
    csv_total_value = sku_7547073_csv['Total Value'].iloc[0]  # Should be same for all rows
    
    print(f"\nCSV Totals for SKU 7547073:")
    print(f"Total Stock: {csv_total_qty}")
    print(f"Total Value: {csv_total_value}")
    
    # Excel data from the image (manually entered)
    excel_total_qty = 265  # From Excel image
    excel_total_value = 5247.78  # From Excel image
    
    print(f"\nExcel Totals for SKU 7547073:")
    print(f"Total Stock: {excel_total_qty}")
    print(f"Total Value: {excel_total_value}")
    
    print(f"\nDifferences:")
    print(f"Quantity difference: {csv_total_qty - excel_total_qty}")
    print(f"Value difference: {csv_total_value - excel_total_value}")
    
    if csv_total_qty == excel_total_qty and abs(csv_total_value - excel_total_value) < 0.01:
        print("✅ MATCH: CSV and Excel totals match!")
    else:
        print("❌ MISMATCH: CSV and Excel totals don't match")

# Check overall totals
print(f"\n=== OVERALL TOTALS COMPARISON ===")

# Calculate CSV totals
csv_total_stock = csv_df.groupby('SKU')['Total Stock'].first().sum()
csv_total_value = csv_df.groupby('SKU')['Total Value'].first().sum()

print(f"CSV Overall Totals:")
print(f"Total Stock: {csv_total_stock:,.2f}")
print(f"Total Value: {csv_total_value:,.2f}")

# Excel totals from the image (bottom row)
excel_overall_stock = 767834.4  # From Excel total row
excel_overall_value = 1020819024  # From Excel total row

print(f"\nExcel Overall Totals:")
print(f"Total Stock: {excel_overall_stock:,.2f}")
print(f"Total Value: {excel_overall_value:,.2f}")

print(f"\nOverall Differences:")
print(f"Stock difference: {csv_total_stock - excel_overall_stock:,.2f}")
print(f"Value difference: {csv_total_value - excel_overall_value:,.2f}")

if abs(csv_total_stock - excel_overall_stock) < 1 and abs(csv_total_value - excel_overall_value) < 100:
    print("✅ CLOSE MATCH: Overall totals are very close")
elif abs(csv_total_stock - excel_overall_stock) / excel_overall_stock < 0.01:
    print("✅ REASONABLE MATCH: Overall totals within 1%")
else:
    print("❌ SIGNIFICANT MISMATCH: Overall totals are very different")

# Check data structure differences
print(f"\n=== DATA STRUCTURE ANALYSIS ===")
print(f"Number of unique SKUs in CSV: {csv_df['SKU'].nunique()}")
print(f"Number of unique Plants in CSV: {csv_df['Plant'].nunique()}")
print(f"Total movement records in CSV: {len(csv_df)}")

# Check if we have the right data distribution
print(f"\nTop 10 SKUs by Total Stock:")
top_skus = csv_df.groupby('SKU')['Total Stock'].first().sort_values(ascending=False).head(10)
print(top_skus)

print(f"\nTop 10 SKUs by Total Value:")
top_value_skus = csv_df.groupby('SKU')['Total Value'].first().sort_values(ascending=False).head(10)
print(top_value_skus)

# Check for any obvious data issues
print(f"\n=== DATA QUALITY CHECKS ===")
print(f"Records with zero quantity: {len(csv_df[csv_df['Quantity moved'] == 0])}")
print(f"Records with zero value: {len(csv_df[csv_df['Value moved'] == 0])}")
print(f"Records with negative quantity: {len(csv_df[csv_df['Quantity moved'] < 0])}")
print(f"Records with negative value: {len(csv_df[csv_df['Value moved'] < 0])}")

# Check date range
csv_df['Entry_Date_Parsed'] = pd.to_datetime(csv_df['Entry date'])
min_date = csv_df['Entry_Date_Parsed'].min()
max_date = csv_df['Entry_Date_Parsed'].max()
print(f"Date range: {min_date} to {max_date}")

print(f"\n=== POSSIBLE REASONS FOR MISMATCH ===")
print("1. Different data sources (CSV vs Excel might be from different extracts)")
print("2. Different filtering criteria applied")
print("3. Different calculation methods for totals")
print("4. Data generation vs real data differences")
print("5. Currency or unit conversion differences")
print("6. Rounding differences in calculations")

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

print("=== FIXING ENTRY DATE ISSUES ===")

# Load the current CSV
df = pd.read_csv('powerbi_upload_simple_consistent.csv')
print(f"Original records: {len(df)}")

# Check current entry dates
print(f"\nCurrent entry date samples:")
print(df['Entry date'].head().tolist())

# The issue might be that we need proper datetime formatting for Power BI
# Let's convert the dates to a format that Power BI will definitely recognize

# Convert entry dates to proper datetime
df['Entry_Date_Fixed'] = pd.to_datetime(df['Entry date'])

# Format for Power BI compatibility (MM/DD/YYYY HH:MM:SS AM/PM)
df['Entry date'] = df['Entry_Date_Fixed'].dt.strftime('%m/%d/%Y %I:%M:%S %p')

print(f"\nFixed entry date samples:")
print(df['Entry date'].head().tolist())

# Also fix the Today column to match
df['Today'] = pd.to_datetime(df['Today']).dt.strftime('%m/%d/%Y')

print(f"\nFixed Today date samples:")
print(df['Today'].head().tolist())

# Ensure all numeric columns are properly formatted
numeric_columns = ['Quantity moved', 'Price', 'Value moved', 'Cumulative Qty', 'Months', 
                  'Total Stock', 'Total Value', 'Assigned', 'Value Assigned']

for col in numeric_columns:
    if col in df.columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')

# Remove the temporary column
df = df.drop('Entry_Date_Fixed', axis=1)

# Save the fixed version
output_file = 'powerbi_upload_fixed_dates.csv'
df.to_csv(output_file, index=False)

print(f"\n✅ Fixed CSV saved as: {output_file}")
print(f"Records: {len(df)}")

# Verify the fix
print(f"\n=== VERIFICATION ===")
sample_data = df.head(3)
for i, row in sample_data.iterrows():
    print(f"Row {i+1}:")
    print(f"  SKU: {row['SKU']}")
    print(f"  Entry date: {row['Entry date']}")
    print(f"  Today: {row['Today']}")
    print(f"  Months: {row['Months']}")
    print(f"  Status: {row['Status']}")
    print()

# Check for any remaining issues
print(f"=== DATA QUALITY CHECK ===")
print(f"Records with 'Entry date' containing '00:00:00': {len(df[df['Entry date'].str.contains('12:00:00 AM')])}")
print(f"Unique entry dates: {df['Entry date'].nunique()}")
print(f"Date range check:")

# Parse a few dates to verify they're valid
try:
    sample_dates = df['Entry date'].head(5)
    for date_str in sample_dates:
        parsed = pd.to_datetime(date_str)
        print(f"  '{date_str}' -> {parsed}")
    print("✅ All sample dates parse correctly")
except Exception as e:
    print(f"❌ Date parsing error: {e}")

# Create a summary for Power BI import
print(f"\n=== POWER BI IMPORT INSTRUCTIONS ===")
print(f"1. Use file: {output_file}")
print(f"2. In Power BI, set 'Entry date' column type to 'Date/Time'")
print(f"3. Set 'Today' column type to 'Date'")
print(f"4. Verify that dates show actual dates, not 00:00:00")
print(f"5. Check that 'Months' column shows proper age calculations")

# Show the data types that Power BI should use
print(f"\n=== RECOMMENDED POWER BI COLUMN TYPES ===")
column_types = {
    'Country': 'Text',
    'SKU': 'Whole Number',
    'Material Description': 'Text',
    'Storage Location': 'Text',
    'Movement type': 'Whole Number',
    'Quantity moved': 'Decimal Number',
    'Plant': 'Text',
    'Price': 'Decimal Number',
    'Value moved': 'Decimal Number',
    'Entry date': 'Date/Time',
    'Cumulative Qty': 'Decimal Number',
    'Today': 'Date',
    'Months': 'Decimal Number',
    'Total Stock': 'Decimal Number',
    'Total Value': 'Decimal Number',
    'Assigned': 'Decimal Number',
    'Value Assigned': 'Decimal Number',
    'Impairment Category': 'Text',
    'Status': 'Text',
    'Week_Num': 'Text'
}

for col, dtype in column_types.items():
    if col in df.columns:
        print(f"  {col}: {dtype}")

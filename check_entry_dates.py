import pandas as pd
import numpy as np
from datetime import datetime

print("=== CHECKING ENTRY DATES IN PBI REPORT ===")

# Load the report
df = pd.read_csv('powerbi_upload_simple_consistent.csv')
print(f"Total records: {len(df)}")

# Check entry date column
print(f"\n=== ENTRY DATE ANALYSIS ===")
print(f"Entry date column type: {df['Entry date'].dtype}")
print(f"Sample entry dates:")
print(df['Entry date'].head(10).tolist())

# Convert to datetime and check for issues
df['Entry_Date_Parsed'] = pd.to_datetime(df['Entry date'], errors='coerce')

# Check for null/invalid dates
null_dates = df['Entry_Date_Parsed'].isnull().sum()
print(f"\nNull/invalid dates: {null_dates}")

if null_dates > 0:
    print("Records with null dates:")
    print(df[df['Entry_Date_Parsed'].isnull()][['SKU', 'Plant', 'Entry date']].head())

# Check date range
valid_dates = df[df['Entry_Date_Parsed'].notnull()]
if len(valid_dates) > 0:
    min_date = valid_dates['Entry_Date_Parsed'].min()
    max_date = valid_dates['Entry_Date_Parsed'].max()
    print(f"\nDate range: {min_date} to {max_date}")

# Check for dates that might be interpreted as "zero" or default
as_on_date = pd.to_datetime('2025-08-31')
same_as_today = len(valid_dates[valid_dates['Entry_Date_Parsed'] == as_on_date])
print(f"Records with entry date same as 'AS ON' date (2025-08-31): {same_as_today}")

# Check months calculation
print(f"\n=== MONTHS CALCULATION CHECK ===")
print(f"Months column type: {df['Months'].dtype}")
print(f"Sample months values:")
print(df['Months'].head(10).tolist())

# Check for zero months
zero_months = len(df[df['Months'] == 0])
print(f"Records with 0 months: {zero_months}")

if zero_months > 0:
    print("Sample records with 0 months:")
    print(df[df['Months'] == 0][['SKU', 'Plant', 'Entry date', 'Months', 'Status']].head())

# Check months distribution
print(f"\nMonths distribution:")
print(df['Months'].describe())

# Check status distribution
print(f"\n=== STATUS DISTRIBUTION ===")
print(df['Status'].value_counts())

# Check if there are any patterns in the dates
print(f"\n=== DATE PATTERNS ===")
df['Entry_Year'] = valid_dates['Entry_Date_Parsed'].dt.year
df['Entry_Month'] = valid_dates['Entry_Date_Parsed'].dt.month

print("Entry dates by year:")
print(df['Entry_Year'].value_counts().sort_index())

print("\nEntry dates by month (recent year):")
recent_data = df[df['Entry_Year'] >= 2024]
if len(recent_data) > 0:
    print(recent_data['Entry_Month'].value_counts().sort_index())

# Check for any obvious issues with the date format
print(f"\n=== DATE FORMAT CHECK ===")
print("First 5 entry dates (raw):")
for i, date_str in enumerate(df['Entry date'].head(5)):
    try:
        parsed = pd.to_datetime(date_str)
        print(f"{i+1}. '{date_str}' -> {parsed}")
    except:
        print(f"{i+1}. '{date_str}' -> FAILED TO PARSE")

# Suggest fixes if needed
print(f"\n=== RECOMMENDATIONS ===")
if null_dates > 0:
    print("❌ ISSUE: Some dates are null/invalid")
    print("   FIX: Need to regenerate with proper date handling")
elif same_as_today == len(df):
    print("❌ ISSUE: All dates are the same (AS ON date)")
    print("   FIX: Need to generate realistic historical dates")
elif zero_months > len(df) * 0.8:
    print("❌ ISSUE: Too many records with 0 months")
    print("   FIX: Need better date distribution for aging analysis")
else:
    print("✅ DATES LOOK GOOD: Entry dates are properly distributed")
    print("   The issue might be in Power BI data type interpretation")
    print("   Suggestion: Check Power BI date column format settings")

# Create a sample with better date formatting for Power BI
print(f"\n=== CREATING POWER BI FRIENDLY DATE FORMAT ===")
df_sample = df.head(5).copy()
df_sample['Entry_Date_PowerBI'] = pd.to_datetime(df_sample['Entry date']).dt.strftime('%m/%d/%Y %I:%M:%S %p')

print("Original vs Power BI friendly format:")
for i in range(len(df_sample)):
    print(f"{df_sample.iloc[i]['Entry date']} -> {df_sample.iloc[i]['Entry_Date_PowerBI']}")

import pandas as pd
import numpy as np

print("=== SIMPLE ANALYSIS: PBI Report vs MB5B Files ===")

# Load our PBI report
pbi_df = pd.read_csv('powerbi_upload_simple_consistent.csv')
print(f"PBI report: {len(pbi_df)} records")

# Load MB5B files
mb5b_09_df = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 09.csv', skiprows=4)
mb5b_36_df = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - MB5B - 36.csv', skiprows=4)

print(f"MB5B 09-month: {len(mb5b_09_df)} records")
print(f"MB5B 36-month: {len(mb5b_36_df)} records")

# Clean material numbers (handle column name with space)
material_col = ' Material' if ' Material' in mb5b_09_df.columns else 'Material'
mb5b_09_df['Material_Clean'] = mb5b_09_df[material_col].astype(str).str.lstrip('0')
mb5b_36_df['Material_Clean'] = mb5b_36_df[material_col].astype(str).str.lstrip('0')

# Get unique materials from each source
pbi_materials = set(pbi_df['SKU'].astype(str))
mb5b_09_materials = set(mb5b_09_df['Material_Clean'])
mb5b_36_materials = set(mb5b_36_df['Material_Clean'])
mb5b_all_materials = mb5b_09_materials.union(mb5b_36_materials)

print(f"\n=== MATERIAL OVERLAP ANALYSIS ===")
print(f"PBI materials: {len(pbi_materials)}")
print(f"MB5B 09-month materials: {len(mb5b_09_materials)}")
print(f"MB5B 36-month materials: {len(mb5b_36_materials)}")
print(f"MB5B combined materials: {len(mb5b_all_materials)}")

# Check overlap
pbi_mb5b_overlap = pbi_materials.intersection(mb5b_all_materials)
print(f"Materials in both PBI and MB5B: {len(pbi_mb5b_overlap)}")

if len(pbi_mb5b_overlap) > 0:
    print(f"Overlap percentage: {len(pbi_mb5b_overlap)/len(pbi_materials)*100:.1f}% of PBI materials")
    print(f"Sample overlapping materials: {list(pbi_mb5b_overlap)[:10]}")
else:
    print("NO OVERLAP found between PBI and MB5B materials!")

# Check what's in MB5B but not in PBI
mb5b_only = mb5b_all_materials - pbi_materials
print(f"Materials only in MB5B: {len(mb5b_only)}")
if len(mb5b_only) > 0:
    print(f"Sample MB5B-only materials: {list(mb5b_only)[:10]}")

# Check what's in PBI but not in MB5B
pbi_only = pbi_materials - mb5b_all_materials
print(f"Materials only in PBI: {len(pbi_only)}")
if len(pbi_only) > 0:
    print(f"Sample PBI-only materials: {list(pbi_only)[:10]}")

# Analyze MB5B impairment data
print(f"\n=== MB5B IMPAIRMENT ANALYSIS ===")

# Clean numeric columns
mb5b_09_df['Closing Stock'] = pd.to_numeric(mb5b_09_df['Closing Stock'], errors='coerce').fillna(0)
mb5b_36_df['Closing Stock'] = pd.to_numeric(mb5b_36_df['Closing Stock'], errors='coerce').fillna(0)

# Find the impairment quantity column
impair_col = None
for col in mb5b_09_df.columns:
    if 'qty to impair' in str(col).lower():
        impair_col = col
        break

if impair_col:
    mb5b_09_df['Qty_To_Impair'] = pd.to_numeric(mb5b_09_df[impair_col], errors='coerce').fillna(0)
    mb5b_36_df['Qty_To_Impair'] = pd.to_numeric(mb5b_36_df[impair_col], errors='coerce').fillna(0)
    
    # Analyze impairments
    mb5b_09_impairments = mb5b_09_df[mb5b_09_df['Qty_To_Impair'] > 0]
    mb5b_36_impairments = mb5b_36_df[mb5b_36_df['Qty_To_Impair'] > 0]
    
    print(f"MB5B 09-month records with impairment: {len(mb5b_09_impairments)}")
    print(f"MB5B 36-month records with impairment: {len(mb5b_36_impairments)}")
    
    if len(mb5b_09_impairments) > 0:
        print(f"Total 09-month impairment quantity: {mb5b_09_impairments['Qty_To_Impair'].sum():,.0f}")
        print("Top 09-month impairments:")
        top_09 = mb5b_09_impairments.nlargest(5, 'Qty_To_Impair')
        print(top_09[['Material_Clean', 'Plant', 'Closing Stock', 'Qty_To_Impair', 'Category']])
    
    if len(mb5b_36_impairments) > 0:
        print(f"Total 36-month impairment quantity: {mb5b_36_impairments['Qty_To_Impair'].sum():,.0f}")
        print("Top 36-month impairments:")
        top_36 = mb5b_36_impairments.nlargest(5, 'Qty_To_Impair')
        print(top_36[['Material_Clean', 'Plant', 'Closing Stock', 'Qty_To_Impair', 'Category']])

# Check if any of the impairment materials are in our PBI report
if impair_col and len(pbi_mb5b_overlap) > 0:
    print(f"\n=== IMPAIRMENT COVERAGE IN PBI ===")
    
    all_impairments = pd.concat([mb5b_09_impairments, mb5b_36_impairments])
    impairment_materials = set(all_impairments['Material_Clean'])
    
    covered_impairments = impairment_materials.intersection(pbi_materials)
    print(f"Impairment materials covered in PBI: {len(covered_impairments)}/{len(impairment_materials)}")
    
    if len(covered_impairments) > 0:
        print(f"Covered impairment materials: {list(covered_impairments)}")

print(f"\n=== CONCLUSION ===")
if len(pbi_mb5b_overlap) == 0:
    print("❌ NO MATCH: Our PBI report and MB5B files contain completely different materials!")
    print("\nPossible reasons:")
    print("1. MB5B files are filtered/processed versions of the data")
    print("2. MB5B files contain only materials with movements or impairments")
    print("3. Different time periods or data extraction logic")
    print("4. MB5B files are based on different source systems")
    
    print(f"\nOur PBI report is based on MB5L data (current stock positions)")
    print(f"MB5B files appear to be movement-based impairment calculations")
    print(f"These are fundamentally different datasets serving different purposes")
    
elif len(pbi_mb5b_overlap) < len(pbi_materials) * 0.5:
    print("⚠️  PARTIAL MATCH: Limited overlap between PBI report and MB5B files")
    print(f"Only {len(pbi_mb5b_overlap)/len(pbi_materials)*100:.1f}% of materials match")
    
else:
    print("✅ GOOD MATCH: Significant overlap between PBI report and MB5B files")
    print(f"{len(pbi_mb5b_overlap)/len(pbi_materials)*100:.1f}% of materials match")

print(f"\nRecommendation:")
print(f"- Use MB5L-based PBI report for current stock positions and FIFO aging")
print(f"- Use MB5B files for movement-based impairment calculations")
print(f"- These serve different analytical purposes and both are valuable")

import pandas as pd
import numpy as np
import sys

# --- File Paths ---
# Our "Source of Truth" is now the output of the perfect reconciliation script
RECONCILIATION_FILE = 'perfect_reconciliation_9_month.csv' 
TRANSACTION_LOG_FILE = 'all_materials_transaction_log.csv'

try:
    print("🔄 Starting FINAL verification: Does the transaction log match our perfect aggregates?")
    
    # --- Step 1: Load the definitive datasets ---
    print("   - Loading the perfect aggregates and the full transaction log...")
    agg_df = pd.read_csv(RECONCILIATION_FILE)
    log_df = pd.read_csv(TRANSACTION_LOG_FILE)

    # --- Step 2: Calculate period movements from the transaction log ---
    print("   - Summing the movements from the transaction log...")
    log_df['Posting_Date'] = pd.to_datetime(log_df['Posting_Date'])
    
    # Filter for movements ONLY within the 9-month period
    period_start = pd.to_datetime('2024-12-01')
    period_end = pd.to_datetime('2025-08-31')
    movements_log = log_df[(log_df['Posting_Date'] >= period_start) & (log_df['Posting_Date'] <= period_end)]
    
    # Sum up the net movement for each material-plant combo
    net_movements = movements_log.groupby(['Material', 'Plant'])['Signed_Quantity'].sum().reset_index()
    net_movements.rename(columns={'Signed_Quantity': 'Calculated_Net_Movement'}, inplace=True)

    # --- Step 3: Verify the core equation ---
    print("   - Verifying the equation: Opening + Movements = Closing")
    
    # **FIX**: Ensure 'Material' columns in both dataframes are the same type (string)
    agg_df['Material'] = agg_df['Material'].astype(str)
    net_movements['Material'] = net_movements['Material'].astype(str)
    
    # Merge the net movements with our perfect aggregates
    verification_df = agg_df.merge(net_movements, on=['Material', 'Plant'], how='left').fillna(0)
    
    # Apply the formula: Does our calculated closing stock match the one from the perfect script?
    verification_df['Calculated_Closing_Stock'] = verification_df['Opening Stock (9m)'] + verification_df['Calculated_Net_Movement']
    
    # Calculate the final difference
    verification_df['Final_Difference'] = (verification_df['Closing Stock (9m)'] - verification_df['Calculated_Closing_Stock']).round(2)
    
    mismatches = verification_df[verification_df['Final_Difference'].abs() > 0.01]

    # --- Final Report ---
    if mismatches.empty:
        print("\n" + "="*60)
        print("🎉🎉🎉 PERFECT MATCH CONFIRMED! 🎉🎉🎉")
        print("The sum of transactions in the log perfectly explains the change")
        print("from the Opening Stock to the Closing Stock in our final aggregates.")
        print("The logic is sound and the data is consistent.")
        print("="*60)
    else:
        print("\n" + "="*60)
        print(f"⚠️ Found {len(mismatches)} discrepancies during the final consistency check.")
        print("This indicates a mismatch between the final aggregates and the transaction log.")
        print("Sample of remaining mismatches:")
        print(mismatches[['Material', 'Plant', 'Closing Stock (9m)', 'Calculated_Closing_Stock', 'Final_Difference']].head())
        print("="*60)

except FileNotFoundError as e:
    print(f"❌ Error: A required file was not found: {e.filename}")
    print("   Please ensure 'perfect_reconciliation_9_month.csv' and 'all_materials_transaction_log.csv' are present.")
except Exception as e:
    print(f"An unexpected error occurred: {e}")
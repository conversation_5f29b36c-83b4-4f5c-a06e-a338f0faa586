import pandas as pd
import numpy as np

print("=== ANALYZING P8 2025 vs GENERATED DATA DISCREPANCY ===")

# Load our generated data
df = pd.read_csv('powerbi_upload_fixed_dates.csv')

print(f"Our generated data:")
print(f"  Total records: {len(df)}")
print(f"  Total Assigned Quantity: {df['Assigned'].sum():,.2f}")
print(f"  Total Assigned Value: {df['Value Assigned'].sum():,.2f}")

# Excel P8 2025 totals from the screenshot
excel_p8_totals = {
    'Others': 394649.13,
    'Glass': 4901.63,
    'Total': 399550.76
}

print(f"\nExcel P8 2025 totals:")
print(f"  Others: {excel_p8_totals['Others']:,.2f}")
print(f"  Glass: {excel_p8_totals['Glass']:,.2f}")
print(f"  Total: {excel_p8_totals['Total']:,.2f}")

# Check our data by category
our_totals = df.groupby('Impairment Category')['Value Assigned'].sum()
print(f"\nOur data totals by category:")
for category, total in our_totals.items():
    print(f"  {category}: {total:,.2f}")

our_total = df['Value Assigned'].sum()
print(f"  Our Total: {our_total:,.2f}")

# Calculate the difference
difference = excel_p8_totals['Total'] - our_total
print(f"\nDifference: {difference:,.2f}")
print(f"Our data is {'higher' if difference < 0 else 'lower'} by {abs(difference):,.2f}")

# Check date ranges in our data
df['Entry_Date_Parsed'] = pd.to_datetime(df['Entry date'])
print(f"\nOur data date range:")
print(f"  Earliest: {df['Entry_Date_Parsed'].min()}")
print(f"  Latest: {df['Entry_Date_Parsed'].max()}")
print(f"  Today date used: {df['Today'].iloc[0]}")

# Check if we have the right period logic
print(f"\n=== POTENTIAL ISSUES ===")

# Issue 1: Period definition
print(f"1. PERIOD DEFINITION:")
print(f"   - Excel shows 'P8 2025' (August 2025)")
print(f"   - Our data uses 'as of 31st August 2025'")
print(f"   - These might be different period cuts")

# Issue 2: Transaction date vs Period
print(f"\n2. TRANSACTION DATES:")
print(f"   - Our transactions are from 2024")
print(f"   - Excel might be using 2025 transactions")
print(f"   - Or different period logic")

# Issue 3: Category mapping
print(f"\n3. CATEGORY MAPPING:")
our_categories = df['Impairment Category'].value_counts()
print(f"   Our categories: {list(our_categories.index)}")
print(f"   Excel categories: ['Others', 'Glass']")

# Check if we need to map categories differently
print(f"\n4. CATEGORY REMAPPING NEEDED:")
if 'GLASS' in our_categories.index:
    print(f"   - We have 'GLASS': {our_categories.get('GLASS', 0)} records")
if 'Glass' in our_categories.index:
    print(f"   - We have 'Glass': {our_categories.get('Glass', 0)} records")
if 'Others' in our_categories.index:
    print(f"   - We have 'Others': {our_categories.get('Others', 0)} records")

# Check plant-wise breakdown
print(f"\n5. PLANT-WISE COMPARISON:")
our_plant_totals = df.groupby('Plant')['Value Assigned'].sum().sort_values(ascending=False)
print(f"Our top plants by value:")
for plant, value in our_plant_totals.head(10).items():
    print(f"   {plant}: {value:,.2f}")

# Excel plant totals from screenshot
excel_plants = {
    'DE02': 152602.78,
    'DE04': 912.22,
    'DE05': 35248.70,
    'DE06': 18031.72,
    'DE07': 332.96,
    'DE08': 6947.22,
    'DE13': 33726.63,
    'DE30': 150262.63,
    'DE34': 1485.90
}

print(f"\nExcel plant totals:")
total_excel_plants = 0
for plant, value in excel_plants.items():
    our_value = our_plant_totals.get(plant, 0)
    diff = our_value - value
    total_excel_plants += value
    print(f"   {plant}: Excel={value:,.2f}, Ours={our_value:,.2f}, Diff={diff:,.2f}")

print(f"\nTotal from Excel plants: {total_excel_plants:,.2f}")
print(f"Excel P8 total: {excel_p8_totals['Total']:,.2f}")
print(f"Difference: {excel_p8_totals['Total'] - total_excel_plants:,.2f}")

# Recommendations
print(f"\n=== RECOMMENDATIONS ===")
print(f"1. CHECK PERIOD DEFINITION:")
print(f"   - Verify if P8 2025 means 'August 2025 transactions' or 'as of August 2025'")
print(f"   - Our logic uses 'as of 31st August 2025' with historical transactions")

print(f"\n2. VERIFY TRANSACTION DATE RANGE:")
print(f"   - Excel might be filtering to 2025 transactions only")
print(f"   - Or using a different date range")

print(f"\n3. CHECK CATEGORY MAPPING:")
print(f"   - Ensure 'Glass'/'GLASS' mapping is consistent")
print(f"   - Verify 'Others' includes all non-glass items")

print(f"\n4. VALIDATE PLANT DATA:")
print(f"   - Some plants show significant differences")
print(f"   - DE02: We have {our_plant_totals.get('DE02', 0):,.2f} vs Excel {excel_plants['DE02']:,.2f}")
print(f"   - DE30: We have {our_plant_totals.get('DE30', 0):,.2f} vs Excel {excel_plants['DE30']:,.2f}")

# Create a reconciliation report
reconciliation_data = []
for plant in set(list(excel_plants.keys()) + list(our_plant_totals.index)):
    excel_val = excel_plants.get(plant, 0)
    our_val = our_plant_totals.get(plant, 0)
    reconciliation_data.append({
        'Plant': plant,
        'Excel_P8_2025': excel_val,
        'Our_Generated': our_val,
        'Difference': our_val - excel_val,
        'Percent_Diff': ((our_val - excel_val) / excel_val * 100) if excel_val > 0 else 0
    })

reconciliation_df = pd.DataFrame(reconciliation_data)
reconciliation_df = reconciliation_df.sort_values('Difference', key=abs, ascending=False)

print(f"\n=== RECONCILIATION REPORT ===")
for _, row in reconciliation_df.iterrows():
    if abs(row['Difference']) > 1000:  # Only show significant differences
        print(f"{row['Plant']}: Excel={row['Excel_P8_2025']:,.0f}, Ours={row['Our_Generated']:,.0f}, "
              f"Diff={row['Difference']:,.0f} ({row['Percent_Diff']:+.1f}%)")

# Save reconciliation report
reconciliation_df.to_csv('plant_reconciliation_report.csv', index=False)
print(f"\n✅ Reconciliation report saved: plant_reconciliation_report.csv")

print(f"\n🎯 NEXT STEPS:")
print(f"1. Clarify what 'P8 2025' means exactly")
print(f"2. Check if we need different transaction date filters")
print(f"3. Verify the period cut-off logic")
print(f"4. Ensure category mappings are correct")
